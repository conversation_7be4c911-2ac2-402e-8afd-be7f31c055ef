import fs from 'fs';
import { executeQuery } from '../db';

interface TransactionRecord {
  id?: number;
  reference_no?: string;
  transaction_id: string;
  transaction_out_id?: string;
  transaction_card_no?: string;
  transaction_merchant_id?: string;
  transaction_merchant_name?: string;
  transaction_merchant_vat?: string;
  transaction_time: Date;
  transaction_amount: number;
  transaction_refund_id?: string;
  transaction_refund_out_id?: string;
  transaction_mch_id?: string;
  transaction_sub_mch_id?: string;
  transaction_trade_type?: string;
  transaction_trade_status?: string;
  transaction_bank_type?: string;
  transaction_fee_type?: string;
  transaction_coupon_amount?: number;
  transaction_file_name: string;
  transaction_file_name_backup?: string;
  transaction_channel_type?: string;
  create_by?: string;
  create_dt?: Date;
  update_by?: string;
  update_dt?: Date;
  file_record_sequence?: number; // New field for tracking record sequence
}

interface ProcessingResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  errors: string[];
  transactions: TransactionRecord[];
}

interface DuplicateHandlingOptions {
  strategy: 'SKIP' | 'UPDATE' | 'LOG_ONLY' | 'MERGE' | 'INSERT_ALL';
  logDuplicates: boolean;
  updateFields?: string[];
}

interface DuplicateLogEntry {
  transaction_id: string;
  original_file: string;
  duplicate_file: string;
  original_amount: number;
  duplicate_amount: number;
  action_taken: string;
  detected_at: Date;
}

interface MerchantInfo {
  merchant_id: string;
  merchant_name: string;
  merchant_vat: string;
}

// Column mapping constants for better performance
interface ColumnMapping {
  TRANSACTION_TIME: number;
  OFFICIAL_ACCOUNT_ID: number;
  VENDOR_ID: number;
  SUB_VENDOR_ID: number;
  DEVICE_ID: number;
  WECHAT_ORDER_NUMBER: number;
  VENDOR_ORDER_NUMBER: number;
  USER_TAG: number;
  TRANSACTION_TYPE: number;
  TRANSACTION_STATUS: number;
  PAYMENT_BANK: number;
  CURRENCY_TYPE: number;
  TOTAL_AMOUNT: number;
  COUPON_AMOUNT: number;
  WECHAT_REFUND_NUMBER: number;
  VENDOR_REFUND_NUMBER: number;
  REFUND_AMOUNT: number;
}

// Optimized column mapping for WeChat CSV format (35 columns)
const WECHAT_COLUMNS: ColumnMapping = {
  TRANSACTION_TIME: 0,
  OFFICIAL_ACCOUNT_ID: 1,
  VENDOR_ID: 2,
  SUB_VENDOR_ID: 3,
  DEVICE_ID: 4,
  WECHAT_ORDER_NUMBER: 5,
  VENDOR_ORDER_NUMBER: 6,
  USER_TAG: 7,
  TRANSACTION_TYPE: 8,
  TRANSACTION_STATUS: 9,
  PAYMENT_BANK: 10,
  CURRENCY_TYPE: 11,
  TOTAL_AMOUNT: 12,
  COUPON_AMOUNT: 13,
  WECHAT_REFUND_NUMBER: 14,
  VENDOR_REFUND_NUMBER: 15,
  REFUND_AMOUNT: 16
};

export class TransactionProcessingService {
  // Cache for merchant lookups to avoid repeated database queries
  private merchantCache = new Map<string, MerchantInfo | null>();

  /**
   * Process CSV/Excel file and extract transaction data - OPTIMIZED VERSION
   */
  async processTransactionFile(
    filePath: string,
    fileName: string,
    backupPath: string,
    currentUser: string = 'SYSTEM'
  ): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: false,
      totalRows: 0,
      processedRows: 0,
      skippedRows: 0,
      errorRows: 0,
      errors: [],
      transactions: []
    };

    try {
      console.log(`📄 OPTIMIZED: Processing transaction file: ${fileName}`);
      const startTime = Date.now();

      // Pre-load all merchants to cache for faster lookups
      await this.preloadMerchantCache();

      // Use optimized CSV parsing
      const rows = await this.optimizedCsvParsing(filePath);
      result.totalRows = rows.length;

      console.log(`� OPTIMIZED: Found ${rows.length} rows in ${Date.now() - startTime}ms`);

      // Find header row efficiently
      const headerRowIndex = this.findHeaderRowIndex(rows);

      // Filter data rows efficiently
      const dataRows = this.filterDataRows(rows, headerRowIndex, result);

      console.log(`📋 OPTIMIZED: Processing ${dataRows.length} data rows`);

      // Determine channel type once
      const channelType = this.determineChannelType(fileName);

      // Batch process transactions for better performance
      const batchSize = 500; // Smaller batches for memory efficiency
      const allParsedTransactions: TransactionRecord[] = [];

      for (let i = 0; i < dataRows.length; i += batchSize) {
        const batch = dataRows.slice(i, i + batchSize);
        console.log(`� OPTIMIZED: Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(dataRows.length/batchSize)} (${batch.length} rows)`);

        const batchTransactions = await this.processBatchOptimized(
          batch, fileName, backupPath, channelType, currentUser, i + headerRowIndex + 2
        );

        allParsedTransactions.push(...batchTransactions.transactions);
        result.errorRows += batchTransactions.errorCount;
        result.errors.push(...batchTransactions.errors);
      }

      // Validate refund transactions efficiently
      const validatedTransactions = this.validateRefundsInBatch(allParsedTransactions);

      result.transactions = validatedTransactions.valid;
      result.processedRows = validatedTransactions.valid.length;
      // No skipped transactions anymore - all are processed

      // Log adjusted refunds (converted to credit_adjust)
      validatedTransactions.adjusted.forEach((txn: TransactionRecord) => {
        console.log(`🔄 OPTIMIZED: Refund transaction converted to credit_adjust (no matching success): ${txn.transaction_id}`);
      });

      result.success = result.errorRows === 0 || result.processedRows > 0;

      const totalTime = Date.now() - startTime;
      console.log(`✅ OPTIMIZED: Processing complete in ${totalTime}ms: ${result.processedRows} processed, ${result.errorRows} errors, ${validatedTransactions.adjusted.length} adjusted to credit_adjust`);

      return result;

    } catch (error) {
      console.error('❌ OPTIMIZED: Error processing transaction file:', error);
      result.errors.push(`File processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * OPTIMIZED: Pre-load all merchants into cache for faster lookups
   */
  private async preloadMerchantCache(): Promise<void> {
    try {
      console.log('🔄 OPTIMIZED: Pre-loading merchant cache...');
      const startTime = Date.now();

      const result = await executeQuery(
        'SELECT merchant_ref, merchant_name, merchant_vat, merchant_id_wechat FROM merchant WHERE active = true'
      );

      for (const row of result.rows) {
        if (row.merchant_id_wechat) {
          this.merchantCache.set(row.merchant_id_wechat, {
            merchant_id: row.merchant_ref,
            merchant_name: row.merchant_name,
            merchant_vat: row.merchant_vat
          });
        }
      }

      console.log(`✅ OPTIMIZED: Loaded ${this.merchantCache.size} merchants in ${Date.now() - startTime}ms`);
    } catch (error) {
      console.error('❌ OPTIMIZED: Error preloading merchant cache:', error);
    }
  }

  /**
   * OPTIMIZED: Fast CSV parsing with minimal overhead - FIXED for WeChat format
   */
  private async optimizedCsvParsing(filePath: string): Promise<string[][]> {
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // For WeChat CSV format, skip Papa Parse and go directly to manual parsing
    // as the format is non-standard with mixed quoting
    console.log('🔄 OPTIMIZED: Using manual parsing for WeChat CSV format');
    return this.manualCsvParsing(fileContent);
  }

  /**
   * OPTIMIZED: Manual CSV parsing for WeChat format - FIXED
   */
  private manualCsvParsing(fileContent: string): string[][] {
    const lines = fileContent.split('\n');
    const rows: string[][] = [];

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // Remove BOM if present
      const cleanLine = trimmedLine.replace(/^\uFEFF/, '');

      if (lineIndex === 0) {
        // Header row - regular comma separation
        const fields = cleanLine.split(',').map(field => field.trim());
        if (fields.length > 5) {
          rows.push(fields);
          console.log(`🔍 OPTIMIZED: Header parsed with ${fields.length} columns`);
        }
      } else {
        // Data row - starts with backtick, uses comma-backtick separation
        if (cleanLine.startsWith('`')) {
          // Remove the leading backtick and split by comma-backtick
          const withoutLeadingTick = cleanLine.substring(1);
          const fields = withoutLeadingTick.split(',`').map(field => field.trim().replace(/\r$/, ''));

          if (fields.length > 5) {
            rows.push(fields);
          }
        } else if (cleanLine.includes(',')) {
          // Fallback for non-backtick lines
          const fields = cleanLine.split(',').map(field => field.trim());
          if (fields.length > 5) {
            rows.push(fields);
          }
        }
      }
    }

    console.log(`✅ OPTIMIZED: Manual parsing completed - ${rows.length} rows`);
    if (rows.length > 1) {
      console.log(`🔍 OPTIMIZED: Sample data row columns: ${rows[1]?.length}`);
      console.log(`🔍 OPTIMIZED: Sample data: [${rows[1]?.slice(0, 3).join(', ')}]`);
    }
    return rows;
  }

  /**
   * OPTIMIZED: Find header row index efficiently
   */
  private findHeaderRowIndex(rows: string[][]): number {
    for (let i = 0; i < Math.min(3, rows.length); i++) {
      const firstCol = rows[i]?.[0]?.replace(/[`\uFEFF]/g, '').trim();
      if (firstCol?.includes('Transaction time') || firstCol?.includes('Official account ID')) {
        console.log(`🔍 OPTIMIZED: Found header row at index ${i}`);
        return i;
      }
    }
    console.log('⚠️ OPTIMIZED: No clear header found, assuming first row is header');
    return 0;
  }

  /**
   * OPTIMIZED: Filter data rows efficiently for extended CSV format
   */
  private filterDataRows(rows: string[][], headerRowIndex: number, result: ProcessingResult): string[][] {
    return rows.slice(headerRowIndex + 1).filter((row, index) => {
      const rowNumber = index + headerRowIndex + 2;

      // Skip empty rows
      if (!row || row.length === 0 || row[0]?.trim() === '') {
        result.skippedRows++;
        return false;
      }

      // Skip summary rows - check for "Total" in first column
      const firstCol = row[0]?.replace(/`/g, '').trim();
      if (firstCol?.includes('Total') || firstCol === 'Total transaction count') {
        console.log(`🔍 OPTIMIZED: Skipping summary row ${rowNumber}: "${firstCol}"`);
        result.skippedRows++;
        return false;
      }

      // Skip rows that are just numbers (summary data)
      if (firstCol && /^\d+\.?\d*$/.test(firstCol)) {
        console.log(`🔍 OPTIMIZED: Skipping numeric summary row ${rowNumber}: "${firstCol}"`);
        result.skippedRows++;
        return false;
      }

      // For extended CSV format, expect at least 17 columns (can have up to 35)
      if (row.length < 17) {
        console.log(`🔍 OPTIMIZED: Skipping row ${rowNumber} with too few columns (${row.length})`);
        result.skippedRows++;
        return false;
      }

      // Validate that this looks like a transaction row (has date/time pattern)
      if (firstCol && !firstCol.match(/\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/)) {
        console.log(`🔍 OPTIMIZED: Skipping row ${rowNumber} - invalid date format: "${firstCol}"`);
        result.skippedRows++;
        return false;
      }

      return true;
    });
  }

  /**
   * OPTIMIZED: Process batch of rows efficiently
   */
  private async processBatchOptimized(
    batch: string[][],
    fileName: string,
    backupPath: string,
    channelType: string,
    currentUser: string,
    startRowNumber: number
  ): Promise<{ transactions: TransactionRecord[]; errorCount: number; errors: string[] }> {
    const transactions: TransactionRecord[] = [];
    const errors: string[] = [];
    let errorCount = 0;

    for (let i = 0; i < batch.length; i++) {
      try {
        const row = batch[i];
        const transaction = await this.parseTransactionRowOptimized(
          row, fileName, backupPath, channelType, currentUser
        );

        if (transaction) {
          transactions.push(transaction);
        }
      } catch (error) {
        errorCount++;
        const rowNumber = startRowNumber + i;
        const errorMsg = `Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
      }
    }

    return { transactions, errorCount, errors };
  }

  /**
   * OPTIMIZED: Parse individual transaction row with cached merchant lookups
   */
  private async parseTransactionRowOptimized(
    row: string[],
    fileName: string,
    backupPath: string,
    channelType: string,
    currentUser: string
  ): Promise<TransactionRecord | null> {
    try {
      // OPTIMIZED: Use direct column access with constants
      const cols = WECHAT_COLUMNS;

      // OPTIMIZED: Fast field extraction with minimal string operations and null safety
      const cleanField = (index: number): string => {
        if (index >= row.length || !row[index]) return '';
        return row[index].replace(/`/g, '').trim();
      };

      // OPTIMIZED: Parse transaction time efficiently
      let transactionTimeStr = cleanField(cols.TRANSACTION_TIME);
      if (!transactionTimeStr) {
        throw new Error('Missing transaction time');
      }

      if (transactionTimeStr.includes(',')) {
        transactionTimeStr = transactionTimeStr.split(',')[0].trim();
      }

      // OPTIMIZED: Fast date parsing with better error handling
      let transactionTime = new Date(transactionTimeStr);
      if (isNaN(transactionTime.getTime())) {
        // Try common format variations
        const isoFormat = transactionTimeStr.replace(/(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})/, '$1T$2');
        transactionTime = new Date(isoFormat);

        if (isNaN(transactionTime.getTime())) {
          throw new Error(`Invalid transaction time: ${transactionTimeStr}`);
        }
      }

      // OPTIMIZED: Get required fields with validation
      const transactionId = cleanField(cols.WECHAT_ORDER_NUMBER);
      if (!transactionId) {
        throw new Error('Missing transaction ID');
      }

      const transactionOutId = cleanField(cols.VENDOR_ORDER_NUMBER);
      const subMchId = cleanField(cols.SUB_VENDOR_ID);

      // OPTIMIZED: Use cached merchant lookup
      const merchantInfo = this.merchantCache.get(subMchId) || null;
      if (!merchantInfo && subMchId) {
        console.warn(`⚠️ OPTIMIZED: No merchant found for sub_mch_id: ${subMchId}`);
      }

      // OPTIMIZED: Parse transaction status and amount efficiently
      const rawTradeStatus = cleanField(cols.TRANSACTION_STATUS);
      const tradeStatus = rawTradeStatus.toLowerCase();
      let transactionAmount = 0;

      if (tradeStatus === 'success') {
        const amountStr = cleanField(cols.TOTAL_AMOUNT);
        transactionAmount = parseFloat(amountStr) || 0;
        if (transactionAmount <= 0) {
          console.warn(`⚠️ OPTIMIZED: Invalid amount for success transaction: ${amountStr}`);
        }
      } else if (tradeStatus === 'refund') {
        const refundAmountStr = cleanField(cols.REFUND_AMOUNT);
        transactionAmount = -(parseFloat(refundAmountStr) || 0);
      } else {
        console.warn(`⚠️ OPTIMIZED: Unknown trade status: "${tradeStatus}"`);
      }

      // OPTIMIZED: Parse coupon amount
      const couponAmountStr = cleanField(cols.COUPON_AMOUNT);
      const couponAmount = parseFloat(couponAmountStr) || 0;

      // OPTIMIZED: Create transaction record efficiently with validation
      const transaction: TransactionRecord = {
        reference_no: transactionId,
        transaction_id: transactionId,
        transaction_out_id: transactionOutId || undefined,
        transaction_card_no: cleanField(cols.OFFICIAL_ACCOUNT_ID) || undefined,
        transaction_merchant_id: merchantInfo?.merchant_id || undefined,
        transaction_merchant_name: merchantInfo?.merchant_name || undefined,
        transaction_merchant_vat: merchantInfo?.merchant_vat || undefined,
        transaction_time: transactionTime,
        transaction_amount: transactionAmount,
        transaction_refund_id: cleanField(cols.WECHAT_REFUND_NUMBER) || undefined,
        transaction_refund_out_id: cleanField(cols.VENDOR_REFUND_NUMBER) || undefined,
        transaction_mch_id: cleanField(cols.VENDOR_ID) || undefined,
        transaction_sub_mch_id: subMchId || undefined,
        transaction_trade_type: cleanField(cols.TRANSACTION_TYPE) || undefined,
        transaction_trade_status: tradeStatus,
        transaction_bank_type: cleanField(cols.PAYMENT_BANK) || undefined,
        transaction_fee_type: cleanField(cols.CURRENCY_TYPE) || 'THB',
        transaction_coupon_amount: couponAmount,
        transaction_file_name: fileName,
        transaction_file_name_backup: backupPath || undefined,
        transaction_channel_type: channelType,
        create_by: currentUser,
        update_by: currentUser
      };

      // OPTIMIZED: Validate required fields before returning
      if (!transaction.transaction_id || !transaction.transaction_time || transaction.transaction_amount === undefined) {
        throw new Error(`Invalid transaction data: ID=${transaction.transaction_id}, Time=${transaction.transaction_time}, Amount=${transaction.transaction_amount}`);
      }

      return transaction;

    } catch (error) {
      console.error('❌ OPTIMIZED: Error parsing transaction row:', error);
      throw error;
    }
  }

  /**
   * OPTIMIZED: Validate refunds in batch efficiently
   * Enhanced: Convert refunds without matching success to credit_adjust
   */
  private validateRefundsInBatch(transactions: TransactionRecord[]): {
    valid: TransactionRecord[];
    adjusted: TransactionRecord[];
  } {
    const valid: TransactionRecord[] = [];
    const adjusted: TransactionRecord[] = [];

    // Create a Set of success transaction IDs for O(1) lookup
    const successTransactionIds = new Set(
      transactions
        .filter(t => t.transaction_trade_status === 'success')
        .map(t => t.transaction_id)
    );

    for (const transaction of transactions) {
      if (transaction.transaction_trade_status === 'refund') {
        // Check if there's NOT a matching success transaction
        if (!successTransactionIds.has(transaction.transaction_id)) {
          // Create a copy and change status to credit_adjust
          const adjustedTransaction = {
            ...transaction,
            transaction_trade_status: 'credit_adjust' as const
          };
          valid.push(adjustedTransaction);
          adjusted.push(adjustedTransaction);
        } else {
          // Refund with matching success transaction remains as refund
          valid.push(transaction);
        }
      } else {
        // Non-refund transactions are always valid
        valid.push(transaction);
      }
    }

    return { valid, adjusted };
  }



  /**
   * Determine channel type from filename
   */
  private determineChannelType(fileName: string): string {
    const lowerFileName = fileName.toLowerCase();
    
    if (lowerFileName.includes('wechat') || lowerFileName.includes('wx')) {
      return 'WeChat';
    } else if (lowerFileName.includes('uni') || lowerFileName.includes('union')) {
      return 'UNIPAY';
    } else if (lowerFileName.includes('alipay')) {
      return 'Alipay';
    }
    
    // Default based on file structure - if it has WeChat-style columns, assume WeChat
    return 'WeChat';
  }

  /**
   * OPTIMIZED: Save transactions to database using high-performance bulk insert
   */
  async saveTransactions(
    transactions: TransactionRecord[],
    _options?: DuplicateHandlingOptions
  ): Promise<{
    success: boolean;
    savedCount: number;
    duplicateCount: number;
    updatedCount: number;
    errorCount: number;
    errors: string[];
    duplicateDetails: DuplicateLogEntry[];
  }> {

    const result = {
      success: false,
      savedCount: 0,
      duplicateCount: 0,
      updatedCount: 0,
      errorCount: 0,
      errors: [] as string[],
      duplicateDetails: [] as DuplicateLogEntry[]
    };

    try {
      console.log(`💾 OPTIMIZED BULK INSERT: Saving ${transactions.length} transactions`);
      const startTime = Date.now();

      if (transactions.length === 0) {
        result.success = true;
        console.log('✅ OPTIMIZED: No transactions to save');
        return result;
      }

      // OPTIMIZED: Use larger batch sizes for better performance
      const batchSize = 2000; // Increased from 1000
      const batches = [];

      for (let i = 0; i < transactions.length; i += batchSize) {
        batches.push(transactions.slice(i, i + batchSize));
      }

      console.log(`📦 OPTIMIZED: Processing ${batches.length} batches of max ${batchSize} records each`);

      // OPTIMIZED: Process batches with better error handling
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`🔄 OPTIMIZED: Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} records`);

        try {
          // OPTIMIZED: Pre-allocate arrays for better performance
          const values: string[] = new Array(batch.length);
          const params: any[] = new Array(batch.length * 23);
          let paramIndex = 1;

          // OPTIMIZED: Build query with minimal string operations
          for (let i = 0; i < batch.length; i++) {
            const transaction = batch[i];
            const baseIndex = i * 23;

            values[i] = `($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, $${paramIndex + 3}, $${paramIndex + 4}, $${paramIndex + 5}, $${paramIndex + 6}, $${paramIndex + 7}, $${paramIndex + 8}, $${paramIndex + 9}, $${paramIndex + 10}, $${paramIndex + 11}, $${paramIndex + 12}, $${paramIndex + 13}, $${paramIndex + 14}, $${paramIndex + 15}, $${paramIndex + 16}, $${paramIndex + 17}, $${paramIndex + 18}, $${paramIndex + 19}, $${paramIndex + 20}, $${paramIndex + 21}, $${paramIndex + 22}, NOW(), NOW())`;

            // OPTIMIZED: Direct array assignment with null handling for database
            params[baseIndex] = transaction.reference_no || null;
            params[baseIndex + 1] = transaction.transaction_id;
            params[baseIndex + 2] = transaction.transaction_out_id || null;
            params[baseIndex + 3] = transaction.transaction_card_no || null;
            params[baseIndex + 4] = transaction.transaction_merchant_id || null;
            params[baseIndex + 5] = transaction.transaction_merchant_name || null;
            params[baseIndex + 6] = transaction.transaction_merchant_vat || null;
            params[baseIndex + 7] = transaction.transaction_time;
            params[baseIndex + 8] = transaction.transaction_amount;
            params[baseIndex + 9] = transaction.transaction_refund_id || null;
            params[baseIndex + 10] = transaction.transaction_refund_out_id || null;
            params[baseIndex + 11] = transaction.transaction_mch_id || null;
            params[baseIndex + 12] = transaction.transaction_sub_mch_id || null;
            params[baseIndex + 13] = transaction.transaction_trade_type || null;
            params[baseIndex + 14] = transaction.transaction_trade_status;
            params[baseIndex + 15] = transaction.transaction_bank_type || null;
            params[baseIndex + 16] = transaction.transaction_fee_type || 'THB';
            params[baseIndex + 17] = transaction.transaction_coupon_amount || 0;
            params[baseIndex + 18] = transaction.transaction_file_name;
            params[baseIndex + 19] = transaction.transaction_file_name_backup || null;
            params[baseIndex + 20] = transaction.transaction_channel_type;
            params[baseIndex + 21] = transaction.create_by;
            params[baseIndex + 22] = transaction.update_by;

            paramIndex += 23;
          }

          // OPTIMIZED: Single bulk insert query with better error handling
          const bulkInsertQuery = `
            INSERT INTO transaction_e_pos (
              reference_no, transaction_id, transaction_out_id, transaction_card_no,
              transaction_merchant_id, transaction_merchant_name, transaction_merchant_vat,
              transaction_time, transaction_amount, transaction_refund_id, transaction_refund_out_id,
              transaction_mch_id, transaction_sub_mch_id, transaction_trade_type, transaction_trade_status,
              transaction_bank_type, transaction_fee_type, transaction_coupon_amount,
              transaction_file_name, transaction_file_name_backup, transaction_channel_type,
              create_by, update_by, create_dt, update_dt
            ) VALUES ${values.join(', ')}
          `;

          // Debug: Log first transaction in batch for validation
          if (batchIndex === 0) {
            console.log(`🔍 OPTIMIZED DEBUG: First transaction in batch:`);
            console.log(`  - ID: ${batch[0]?.transaction_id}`);
            console.log(`  - Amount: ${batch[0]?.transaction_amount}`);
            console.log(`  - Time: ${batch[0]?.transaction_time}`);
            console.log(`  - Status: ${batch[0]?.transaction_trade_status}`);
            console.log(`🔍 OPTIMIZED DEBUG: Sample SQL parameters (first 10):`, params.slice(0, 10));
          }

          const insertResult = await executeQuery(bulkInsertQuery, params);
          const insertedCount = insertResult.rowCount || 0;

          result.savedCount += insertedCount;

          console.log(`✅ OPTIMIZED: Batch ${batchIndex + 1} complete: ${insertedCount} records inserted`);

        } catch (error) {
          console.error(`❌ OPTIMIZED: Error processing batch ${batchIndex + 1}:`, error);

          result.errorCount += batch.length;
          const errorMsg = `Batch ${batchIndex + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
        }
      }

      // Success if we inserted any records
      result.success = result.savedCount > 0;

      const totalTime = Date.now() - startTime;
      console.log(`✅ OPTIMIZED BULK INSERT complete in ${totalTime}ms: ${result.savedCount} records inserted, ${result.errorCount} errors`);
      console.log(`📊 OPTIMIZED: Performance: ${Math.round(result.savedCount / (totalTime / 1000))} records/second`);

      return result;

    } catch (error) {
      console.error('❌ OPTIMIZED: Error in BULK INSERT operation:', error);
      result.errors.push(`Database error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }


}
