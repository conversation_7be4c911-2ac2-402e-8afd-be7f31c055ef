import React from 'react';
import { Document, Page, Text, View, StyleSheet, PDFDownloadLink, pdf, Image } from '@react-pdf/renderer';
// Import logo
import logoImg from '../assets/E-POS-logo-1.png';

// Use built-in fonts for better compatibility
// React-pdf comes with these built-in fonts: Helvetica, Times-Roman, Courier
// If you want to try custom fonts later, uncomment the registration below:

/*
// Alternative: Register Arial-like font using a more reliable source
Font.register({
  family: 'ArialLike',
  fonts: [
    {
      src: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap',
      fontWeight: 'normal',
    },
    {
      src: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap',
      fontWeight: 'bold',
    },
  ],
});
*/

interface DisputeFormData {
  requestDate: string
  requestId: string
  institutionName: string
  iin: string
  contactName: string
  phone: string
  fax: string
  email: string
  title: "Mr." | "Ms."
  cardNumber: string
  transactionDate: string
  transactionTime: string
  merchantId: string
  transactionAmount: string
  transactionType: string
  referenceNumber: string
  terminalId: string
  currencyCode: string
  merchantName: string
  disputedAmount: string
  amountType: "full" | "partial"
  disputeType: string[]
  reasonCode: string
  supportingDocs: "yes" | "no"
  pages: string
  additionalMessage: string
}

const defaultData: DisputeFormData = {
  requestDate: "25/07/01",
  requestId: "3180725",
  institutionName: "E-POS Service Company Limited",
  iin: "30340764",
  contactName: "Urawee Sittichai",
  phone: "(662) 8215459",
  fax: "",
  email: "<EMAIL>",
  title: "Ms.",
  cardNumber: "wxc06fb3d2635dcf8f",
  transactionDate: "27/06/2025",
  transactionTime: "16:23:21",
  merchantId: "668905944000009",
  transactionAmount: "76,333.00",
  transactionType: "42000027192025062745",
  referenceNumber: "*********",
  terminalId: "",
  currencyCode: "764",
  merchantName: "T Z W CO.,LTD",
  disputedAmount: "72,516.00",
  amountType: "full",
  disputeType: ["Representment (Credit Card)"],
  reasonCode: "",
  supportingDocs: "yes",
  pages: "1",
  additionalMessage: "-Refund diff amount to customer.",
}

// PDF Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: 'white',
    padding: 20,
    fontSize: 9,
    fontFamily: 'Helvetica',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  logo: {
    width: 120,
    height: 30,
  },
  header: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  requestInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: 'black',
    fontSize: 10,
  },
  section: {
    border: '1pt solid black',
    marginBottom: 8,
  },
  sectionHeader: {
    backgroundColor: '#000',
    color: '#fff',
    padding: 4,
    fontSize: 10,
    fontWeight: 400,
    borderBottomWidth: 1,
    borderBottomColor: 'black',
  },
  sectionContent: {
    padding: 6,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 4,
    gap: 10,
  },
  field: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  fieldLabel: {
    fontSize: 8,
    fontWeight: 400,
    minWidth: 60,
  },
  fieldValue: {
    fontSize: 9,
    // borderBottomWidth: 1,
    // borderBottomColor: '#ccc',
    paddingBottom: 1,
    minHeight: 12,
    flex: 1,
  },
  checkbox: {
    width: 12,
    height: 12,
    border: '1pt solid black',
    marginRight: 3,
    textAlign: 'center',
    fontSize: 10,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    fontSize: 9,
  },
  disputeInformation: {
    minHeight: 220,
  },
  disputeTypeSection: {
    marginTop: 4,
    marginBottom: 4,
  },
  disputeTypeLabel: {
    fontSize: 8,
    fontWeight: 400,
    marginBottom: 2,
  },
  disputeCheckboxes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 3,
  },
  disputeCheckboxItem: {
    width: '50%',
    marginBottom: 2,
  },
  signatureSection: {
    flexDirection: 'row',
    marginTop: 10,
    gap: 15,
  },
  signatureBox: {
    height: 60,
    padding: 3,
    marginTop: 5,
  },
  processingSection: {
    minHeight: 100,
    marginTop: 10,
  },
  fullWidthField: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    marginTop: 4,
  },
});

// PDF Document Component
const DisputeFormDocument: React.FC<{ data: DisputeFormData }> = ({ data }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Logo */}
      <View style={styles.logoContainer}>
        <Image 
          style={styles.logo} 
          src={logoImg}
        />
      </View>

      {/* Header */}
      <Text style={styles.header}>Exhibit 4 Dispute Resolution Form</Text>

      {/* Request Information */}
      <View style={styles.requestInfo}>
        <Text>Request Date: {data.requestDate}</Text>
        <Text>Request ID: {data.requestId}</Text>
      </View>

      {/* Institution Information */}
      <View style={styles.section}>
        <Text style={styles.sectionHeader}>Institution Information</Text>
        <View style={styles.sectionContent}>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Institution Name</Text>
              <Text style={styles.fieldValue}>{data.institutionName}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>IIN</Text>
              <Text style={styles.fieldValue}>{data.iin}</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Contact Name</Text>
              <Text style={styles.fieldValue}>{data.contactName}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Phone</Text>
              <Text style={styles.fieldValue}>{data.phone}</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Fax</Text>
              <Text style={styles.fieldValue}>{data.fax}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Email</Text>
              <Text style={styles.fieldValue}>{data.email}</Text>
            </View>
          </View>
          <View style={styles.checkboxRow}>
            <Text style={styles.checkbox}>{data.title === "Mr." ? "X" : ""}</Text>
            <Text>Mr.</Text>
            <Text style={[styles.checkbox, { marginLeft: 20 }]}>{data.title === "Ms." ? "X" : ""}</Text>
            <Text>Ms.</Text>
          </View>
        </View>
      </View>

      {/* Transaction Information */}
      <View style={styles.section}>
        <Text style={styles.sectionHeader}>Transaction Information</Text>
        <View style={styles.sectionContent}>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Card Number</Text>
              <Text style={styles.fieldValue}>{data.cardNumber}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Merchant Name</Text>
              <Text style={styles.fieldValue}>{data.merchantName}</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Transaction Date</Text>
              <Text style={styles.fieldValue}>{data.transactionDate}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Reference Number</Text>
              <Text style={styles.fieldValue}>{data.referenceNumber}</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Transaction Time</Text>
              <Text style={styles.fieldValue}>{data.transactionTime}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Terminal ID</Text>
              <Text style={styles.fieldValue}>{data.terminalId}</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Merchant ID</Text>
              <Text style={styles.fieldValue}>{data.merchantId}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Currency Code</Text>
              <Text style={styles.fieldValue}>{data.currencyCode}</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Transaction Amount</Text>
              <Text style={styles.fieldValue}>{data.transactionAmount}</Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Transaction Type</Text>
              <Text style={styles.fieldValue}>{data.transactionType}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Dispute Information */}
      <View style={[styles.section, styles.disputeInformation]}>
        <Text style={styles.sectionHeader}>Dispute Information</Text>
        <View style={styles.sectionContent}>
          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Disputed Amount</Text>
              <Text style={styles.fieldValue}>{data.disputedAmount}</Text>
            </View>
            <View style={styles.field}>
              <View style={styles.checkboxRow}>
                <Text style={styles.checkbox}>{data.amountType === "full" ? "X" : ""}</Text>
                <Text>Full Amount</Text>
              </View>
              <View style={styles.checkboxRow}>
                <Text style={styles.checkbox}>{data.amountType === "partial" ? "X" : ""}</Text>
                <Text>Partial Amount</Text>
              </View>
            </View>
          </View>

          <View style={styles.disputeTypeSection}>
            <Text style={styles.disputeTypeLabel}>Dispute Resolution Type</Text>
            <View style={styles.disputeCheckboxes}>
              <View style={styles.disputeCheckboxItem}>
                <View style={styles.checkboxRow}>
                  <Text style={styles.checkbox}>{data.disputeType.includes("Credit Adjustment") ? "X" : ""}</Text>
                  <Text>Credit Adjustment</Text>
                </View>
              </View>
              <View style={styles.disputeCheckboxItem}>
                <View style={styles.checkboxRow}>
                  <Text style={styles.checkbox}>{data.disputeType.includes("First Chargeback") ? "X" : ""}</Text>
                  <Text>First Chargeback</Text>
                </View>
              </View>
              <View style={styles.disputeCheckboxItem}>
                <View style={styles.checkboxRow}>
                  <Text style={styles.checkbox}>{data.disputeType.includes("Debit Adjustment (Credit Card)") ? "X" : ""}</Text>
                  <Text>Debit Adjustment (Credit Card)</Text>
                </View>
              </View>
              <View style={styles.disputeCheckboxItem}>
                <View style={styles.checkboxRow}>
                  <Text style={styles.checkbox}>{data.disputeType.includes("Second Chargeback (Credit Card,Non-ATM)") ? "X" : ""}</Text>
                  <Text>Second Chargeback (Credit Card,Non-ATM)</Text>
                </View>
              </View>
            </View>
            <View style={styles.checkboxRow}>
              <Text style={styles.checkbox}>{data.disputeType.includes("Representment (Credit Card)") ? "X" : ""}</Text>
              <Text>Representment (Credit Card)</Text>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Currency Code</Text>
              <Text style={styles.fieldValue}>{data.currencyCode}</Text>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Supporting Documents</Text>
              <View style={styles.checkboxRow}>
                <Text style={styles.checkbox}>{data.supportingDocs === "yes" ? "X" : ""}</Text>
                <Text>Yes</Text>
                <Text style={[styles.checkbox, { marginLeft: 20 }]}>{data.supportingDocs === "no" ? "X" : ""}</Text>
                <Text>No</Text>
              </View>
            </View>
          </View>

          <View style={styles.fullWidthField}>
            <Text style={styles.fieldLabel}>Additional Message (Reasons for Dispute Resolution)</Text>
            <Text style={[styles.fieldValue, { borderBottomWidth: 1, borderBottomColor: '#ccc' }]}>{data.additionalMessage}</Text>
          </View>
        </View>
      </View>

      {/* Authorized Signature */}
      <View style={styles.section}>
        <Text style={styles.sectionHeader}>Authorized Signature</Text>
        <View style={styles.sectionContent}>
          <View style={styles.signatureBox}>
            <Text style={styles.fieldLabel}>Authorized By</Text>
            <Text style={{ fontSize: 7, marginTop: 10 }}>(Signature and/or Stamp)</Text>
          </View>
        </View>
      </View>

      {/* Processing Result */}
      <View style={[styles.section, styles.processingSection]}>
        <Text style={styles.sectionHeader}>Processing Result (CPU Use Only)</Text>
        <View style={styles.sectionContent}>
          <View style={styles.checkboxRow}>
            <Text style={styles.checkbox}></Text>
            <Text>Not Processed</Text>
            <Text style={[styles.checkbox, { marginLeft: 20 }]}></Text>
            <Text>Processed</Text>
          </View>
          <View style={[styles.row , { marginTop: 5 }]}>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Processing by</Text>
              <Text style={styles.fieldValue}></Text>
            </View>
            <View style={styles.field}>
              <Text style={styles.fieldLabel}>Processed Date</Text>
              <Text style={styles.fieldValue}></Text>
            </View>
          </View>
          <View style={styles.fullWidthField}>
            <Text style={styles.fieldLabel}>Message</Text>
            <Text style={[styles.fieldValue, { borderBottomWidth: 1, borderBottomColor: '#ccc' }]}></Text>
          </View>
        </View>
      </View>
    </Page>
  </Document>
);

// Main Component with Download and Print functionality
interface DisputeFormPDFProps {
  data?: DisputeFormData;
  onDownload?: () => void;
  onPrint?: () => void;
}

const DisputeFormPDF: React.FC<DisputeFormPDFProps> = ({ 
  data = defaultData, 
  onDownload,
  onPrint 
}) => {
  const handleViewPDF = async () => {
    try {
      // Generate PDF blob
      const blob = await pdf(<DisputeFormDocument data={data} />).toBlob();
      
      // Create object URL
      const url = URL.createObjectURL(blob);
      
      // Open in new window for viewing (without auto-print)
      const viewWindow = window.open(url, '_blank');
      
      if (viewWindow) {
        // Clean up URL after a short delay
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 1000);
      } else {
        throw new Error('Unable to open view window');
      }
    } catch (error) {
      console.error('Error viewing PDF:', error);
      throw error;
    }
  };

  return (
    <div className="dispute-form-pdf">
      <div className="flex gap-3">
        {/* Download PDF Button */}
        <PDFDownloadLink
          document={<DisputeFormDocument data={data} />}
          fileName={`dispute-resolution-form-${data.requestId}.pdf`}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors"
        >
          {({ loading }) => (
            loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating PDF...
              </>
            ) : (
              <>
                📄 Download PDF
              </>
            )
          )}
        </PDFDownloadLink>

        {/* View PDF Button */}
        <button
          onClick={handleViewPDF}
          className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors"
        >
          �️ View PDF
        </button>
      </div>
    </div>
  );
};

export default DisputeFormPDF;
export { DisputeFormDocument, type DisputeFormData };
